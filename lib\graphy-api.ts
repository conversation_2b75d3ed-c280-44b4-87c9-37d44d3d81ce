/**
 * Graphy API Service
 * 
 * This module provides functions to interact with Graphy API endpoints
 * for course management, user creation, and enrollment tracking.
 */

import {
  getGraphyConfig,
  buildGraphyQueryString,
  getGraphyHeaders,
  isGraphyApiError,
  getGraphyErrorMessage,
  transformGraphyCourse,
  type GraphyCourse,
  type GraphyLearner,
  type GraphyEnrollment
} from './graphy-config'

export class GraphyApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public originalError?: any
  ) {
    super(message)
    this.name = 'GraphyApiError'
  }
}

/**
 * Fetch all courses from Graphy
 */
export async function fetchGraphyCourses(options: {
  skip?: number
  limit?: number
  query?: Record<string, any>
} = {}): Promise<GraphyCourse[]> {
  try {
    const config = getGraphyConfig()
    const { skip = 0, limit = 50, query = {} } = options

    const queryParams = buildGraphyQueryString({
      mid: config.mid,
      key: config.apiKey,
      skip,
      limit,
      query
    })

    const response = await fetch(`${config.baseUrl}/products?${queryParams}`, {
      method: 'GET',
      headers: getGraphyHeaders()
    })

    if (!response.ok) {
      throw new GraphyApiError(
        `Failed to fetch courses: ${response.statusText}`,
        response.status
      )
    }

    const data = await response.json()

    if (isGraphyApiError(data)) {
      throw new GraphyApiError(getGraphyErrorMessage(data))
    }

    if (!data.data || !Array.isArray(data.data)) {
      throw new GraphyApiError('Invalid response format from Graphy API')
    }

    return data.data.map(transformGraphyCourse)
  } catch (error) {
    if (error instanceof GraphyApiError) {
      throw error
    }
    throw new GraphyApiError(
      'Failed to fetch courses from Graphy',
      500,
      error
    )
  }
}

/**
 * Create a learner on Graphy
 */
export async function createGraphyLearner(learner: GraphyLearner): Promise<{ success: boolean; message: string }> {
  try {
    const config = getGraphyConfig()

    const formData = new URLSearchParams({
      mid: config.mid,
      key: config.apiKey,
      email: learner.email,
      name: learner.name,
      sendEmail: learner.sendEmail !== false ? 'true' : 'false' // Default to true
    })

    if (learner.password) {
      formData.append('password', learner.password)
    } else {
      // Generate a random password if none provided
      formData.append('password', generateRandomPassword())
    }

    const response = await fetch(`${config.baseUrl}/learners`, {
      method: 'POST',
      headers: getGraphyHeaders(),
      body: formData.toString()
    })

    const data = await response.json()

    if (!response.ok) {
      // Check if learner already exists
      if (response.status === 409 || data.message?.includes('already exists') || data.message?.includes('duplicate')) {
        return {
          success: true,
          message: 'Learner already exists'
        }
      }

      throw new GraphyApiError(
        `Failed to create learner: ${data.message || response.statusText}`,
        response.status
      )
    }

    if (isGraphyApiError(data)) {
      throw new GraphyApiError(getGraphyErrorMessage(data))
    }

    return {
      success: true,
      message: 'Learner created successfully'
    }
  } catch (error) {
    if (error instanceof GraphyApiError) {
      throw error
    }
    throw new GraphyApiError(
      'Failed to create learner on Graphy',
      500,
      error
    )
  }
}

/**
 * Get learner enrollments from Graphy
 */
export async function fetchGraphyLearnerEnrollments(email: string): Promise<GraphyEnrollment[]> {
  try {
    const config = getGraphyConfig()

    // Create URL-encoded query parameter for email
    const emailQuery = JSON.stringify({ email })
    const encodedQuery = encodeURIComponent(emailQuery)

    const queryParams = new URLSearchParams({
      mid: config.mid,
      key: config.apiKey,
      query: encodedQuery,
      courseInfo: 'true'
    })

    console.log('🔍 Fetching Graphy learner enrollments for:', email)
    console.log('📡 Query params:', queryParams.toString())

    const response = await fetch(`${config.baseUrl}/learners?${queryParams}`, {
      method: 'GET',
      headers: getGraphyHeaders()
    })

    if (!response.ok) {
      throw new GraphyApiError(
        `Failed to fetch learner enrollments: ${response.statusText}`,
        response.status
      )
    }

    const data = await response.json()
    console.log('📥 Graphy API response:', JSON.stringify(data, null, 2))

    if (isGraphyApiError(data)) {
      throw new GraphyApiError(getGraphyErrorMessage(data))
    }

    if (!data.data || !Array.isArray(data.data)) {
      console.log('⚠️ No learner data found in response')
      return []
    }

    const learner = data.data.find((l: any) => l.email === email)
    if (!learner) {
      console.log('⚠️ Learner not found for email:', email)
      return []
    }

    // Parse the new response format where courses are in the "courses" array
    if (!learner.courses || !Array.isArray(learner.courses)) {
      console.log('⚠️ No courses found for learner:', email)
      return []
    }

    console.log('✅ Found courses for learner:', learner.courses.length)

    // Transform the courses to enrollments format
    return learner.courses.map((course: any) => ({
      productId: course.id,
      title: course.Title || course.title || 'Unknown Course',
      enrolledAt: learner['created date'] || new Date().toISOString(),
      progress: 0, // Progress not available in this format
      status: 'active' as const
    }))
  } catch (error) {
    console.error('❌ Error fetching Graphy learner enrollments:', error)
    if (error instanceof GraphyApiError) {
      throw error
    }
    throw new GraphyApiError(
      'Failed to fetch learner enrollments from Graphy',
      500,
      error
    )
  }
}

/**
 * Check if a learner exists on Graphy
 */
export async function checkGraphyLearnerExists(email: string): Promise<boolean> {
  try {
    const config = getGraphyConfig()

    // Create URL-encoded query parameter for email
    const emailQuery = JSON.stringify({ email })
    const encodedQuery = encodeURIComponent(emailQuery)

    const queryParams = new URLSearchParams({
      mid: config.mid,
      key: config.apiKey,
      query: encodedQuery
    })

    const response = await fetch(`${config.baseUrl}/learners?${queryParams}`, {
      method: 'GET',
      headers: getGraphyHeaders()
    })

    if (!response.ok) {
      return false
    }

    const data = await response.json()

    if (isGraphyApiError(data)) {
      return false
    }

    return data.data && Array.isArray(data.data) && data.data.length > 0
  } catch (error) {
    return false
  }
}

/**
 * Get a specific course by product ID
 */
export async function fetchGraphyCourse(productId: string): Promise<GraphyCourse | null> {
  try {
    const courses = await fetchGraphyCourses({
      query: { productId }
    })
    return courses.find(course => course.productId === productId) || null
  } catch (error) {
    throw new GraphyApiError(
      `Failed to fetch course ${productId}`,
      500,
      error
    )
  }
}



/**
 * Get Graphy course URL for purchase/enrollment
 */
export function getGraphyCourseUrl(slug: string): string {
  const config = getGraphyConfig()
  return `https://${config.subdomain}.graphy.com/courses/${slug}`
}

/**
 * Search courses by title or description
 */
export async function searchGraphyCourses(searchTerm: string, limit = 20): Promise<GraphyCourse[]> {
  try {
    return await fetchGraphyCourses({
      limit,
      query: {
        $or: [
          { title: { $regex: searchTerm, $options: 'i' } },
          { description: { $regex: searchTerm, $options: 'i' } }
        ]
      }
    })
  } catch (error) {
    throw new GraphyApiError(
      'Failed to search courses',
      500,
      error
    )
  }
}

/**
 * Generate a random password for new learners
 */
function generateRandomPassword(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let password = ''
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return password
}
